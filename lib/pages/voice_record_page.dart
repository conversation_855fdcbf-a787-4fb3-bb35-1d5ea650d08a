import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'dart:async';
import 'voice_result_page.dart';

class VoiceRecordPage extends StatefulWidget {
  const VoiceRecordPage({super.key});

  @override
  State<VoiceRecordPage> createState() => _VoiceRecordPageState();
}

class _VoiceRecordPageState extends State<VoiceRecordPage>
    with TickerProviderStateMixin {
  late stt.SpeechToText _speech;
  bool _isListening = false;
  bool _speechEnabled = false;
  String _recognizedText = '';
  double _confidence = 0.0;
  
  // 录音相关状态
  Timer? _recordingTimer;
  int _recordingDuration = 0; // 录音时长（秒）
  static const int _minRecordingDuration = 1; // 最小录音时长（秒）
  
  // 动画控制器
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;
  
  // 手势相关
  bool _isDragging = false;
  double _dragDistance = 0.0;
  static const double _cancelThreshold = 100.0; // 上滑取消的阈值

  // 文本输入控制器（当语音识别不可用时）
  final TextEditingController _textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initSpeech();
    _initAnimations();
  }

  void _initAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  void _initSpeech() async {
    try {
      _speech = stt.SpeechToText();
      _speechEnabled = await _speech.initialize(
        onError: (error) {
          print('Speech recognition error: $error');
          if (mounted) {
            _showErrorDialog('语音识别错误: $error');
            _stopListening();
          }
        },
        onStatus: (status) {
          print('Speech recognition status: $status');
          if (status == 'done' || status == 'notListening') {
            _stopListening();
          }
        },
      );

      if (!_speechEnabled) {
        print('Speech recognition not available');
        if (mounted) {
          _showErrorDialog('语音识别功能不可用。请检查设备权限设置或在真实设备上测试。');
        }
      }
    } catch (e) {
      print('Failed to initialize speech recognition: $e');
      _speechEnabled = false;
      if (mounted) {
        _showErrorDialog('初始化语音识别失败: $e\n\n注意：iOS模拟器可能不支持语音识别，请在真实设备上测试。');
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  void _startListening() async {
    if (!_speechEnabled) {
      _showErrorDialog('语音识别功能不可用');
      return;
    }

    try {
      setState(() {
        _isListening = true;
        _recognizedText = '';
        _confidence = 0.0;
        _recordingDuration = 0;
      });

      // 开始动画
      _pulseController.repeat(reverse: true);

      // 开始计时
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _recordingDuration++;
          });
        }
      });

      await _speech.listen(
        onResult: (result) {
          if (mounted) {
            setState(() {
              _recognizedText = result.recognizedWords;
              _confidence = result.confidence;
            });
          }
        },
        listenFor: const Duration(minutes: 2),
        pauseFor: const Duration(seconds: 3),
        localeId: 'zh_CN', // 支持中文
        listenOptions: stt.SpeechListenOptions(
          partialResults: true,
          cancelOnError: true,
          listenMode: stt.ListenMode.confirmation,
        ),
      );
    } catch (e) {
      print('Error starting speech recognition: $e');
      _showErrorDialog('开始录音失败: $e');
      _stopListening();
    }
  }

  void _stopListening() {
    if (!_isListening) return;
    
    _speech.stop();
    _recordingTimer?.cancel();
    _pulseController.stop();
    _scaleController.reverse();
    
    setState(() {
      _isListening = false;
    });
    
    // 检查录音时长
    if (_recordingDuration < _minRecordingDuration) {
      _showErrorDialog('录音时间过短，请重新录制');
      return;
    }
    
    // 检查是否有识别结果
    if (_recognizedText.isEmpty) {
      _showErrorDialog('未识别到语音内容，请重新录制');
      return;
    }
    
    // 显示确认对话框
    _showConfirmationDialog();
  }

  void _cancelRecording() {
    if (!_isListening) return;
    
    _speech.cancel();
    _recordingTimer?.cancel();
    _pulseController.stop();
    _scaleController.reverse();
    
    setState(() {
      _isListening = false;
      _recognizedText = '';
      _confidence = 0.0;
      _recordingDuration = 0;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('录音已取消')),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('语音识别不可用'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 16),
            const Text('您可以选择：'),
            const Text('1. 在真实设备上测试语音功能'),
            const Text('2. 使用文本输入模式进行测试'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('返回'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showTextInputDialog();
            },
            child: const Text('文本输入'),
          ),
        ],
      ),
    );
  }

  void _showTextInputDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('文本输入模式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('请输入交易信息（模拟语音输入）：'),
            const SizedBox(height: 16),
            TextField(
              controller: _textController,
              decoration: const InputDecoration(
                hintText: '例如：我在星巴克花了35块钱买咖啡',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_textController.text.trim().isNotEmpty) {
                Navigator.of(context).pop();
                _navigateToResultWithText(_textController.text.trim());
              }
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }

  void _navigateToResultWithText(String text) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VoiceResultPage(
          recognizedText: text,
          confidence: 1.0, // 文本输入模式设置为100%置信度
          recordingDuration: 0, // 文本输入模式没有录音时长
        ),
      ),
    );
  }

  void _showConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认提交'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('录音时长: ${_recordingDuration}秒'),
            const SizedBox(height: 8),
            Text('识别内容: $_recognizedText'),
            const SizedBox(height: 8),
            Text('置信度: ${(_confidence * 100).toStringAsFixed(1)}%'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('重新录制'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToResult();
            },
            child: const Text('确认提交'),
          ),
        ],
      ),
    );
  }

  void _navigateToResult() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VoiceResultPage(
          recognizedText: _recognizedText,
          confidence: _confidence,
          recordingDuration: _recordingDuration,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _recordingTimer?.cancel();
    _pulseController.dispose();
    _scaleController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('语音录制'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              // 状态显示区域
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (!_speechEnabled) ...[
                      const Text(
                        '语音识别不可用',
                        style: TextStyle(fontSize: 18, color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'iOS模拟器不支持语音识别\n请在真实设备上测试',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _showTextInputDialog,
                        child: const Text('使用文本输入测试'),
                      ),
                    ]
                    else if (_isListening) ...[
                      Text(
                        '正在录音... $_recordingDuration s',
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 20),
                      if (_recognizedText.isNotEmpty) ...[
                        const Text(
                          '识别内容:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 10),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _recognizedText,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ],
                    ] else
                      const Text(
                        '按住下方按钮开始录音\n上滑取消录制',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 18),
                      ),
                  ],
                ),
              ),
              
              // 录音按钮区域
              Expanded(
                flex: 1,
                child: Center(
                  child: GestureDetector(
                    onPanStart: (details) {
                      if (!_speechEnabled) return;
                      _isDragging = true;
                      _dragDistance = 0.0;
                      _scaleController.forward();
                      _startListening();
                    },
                    onPanUpdate: (details) {
                      if (!_isDragging || !_isListening) return;
                      
                      setState(() {
                        _dragDistance = -details.localPosition.dy;
                      });
                      
                      // 如果上滑距离超过阈值，显示取消提示
                      if (_dragDistance > _cancelThreshold) {
                        // 可以在这里添加视觉反馈
                      }
                    },
                    onPanEnd: (details) {
                      if (!_isDragging) return;
                      
                      _isDragging = false;
                      
                      if (_dragDistance > _cancelThreshold) {
                        _cancelRecording();
                      } else {
                        _stopListening();
                      }
                      
                      setState(() {
                        _dragDistance = 0.0;
                      });
                    },
                    child: AnimatedBuilder(
                      animation: Listenable.merge([_pulseAnimation, _scaleAnimation]),
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value * (_isListening ? _pulseAnimation.value : 1.0),
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _isListening 
                                  ? (_dragDistance > _cancelThreshold ? Colors.red : Colors.blue)
                                  : (_speechEnabled ? Colors.blue : Colors.grey),
                              boxShadow: _isListening
                                  ? [
                                      BoxShadow(
                                        color: Colors.blue.withOpacity(0.3),
                                        blurRadius: 20,
                                        spreadRadius: 5,
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Icon(
                              _dragDistance > _cancelThreshold ? Icons.close : Icons.mic,
                              size: 60,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              
              // 提示文本
              if (_isListening && _dragDistance > _cancelThreshold)
                const Text(
                  '松手取消录制',
                  style: TextStyle(color: Colors.red, fontSize: 16),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
